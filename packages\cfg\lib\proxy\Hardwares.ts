import { <PERSON><PERSON><PERSON><PERSON>indow } from 'electron'
import { HardwareInfo, ICfg, IEngine, IHardwares } from '../interfaces'
import { BaseProxy, method } from './Base'
import { cust } from '../proto'

export class Hardwares extends BaseProxy<IHardwares> implements IHardwares {
  private _hardwares: HardwareInfo[]

  constructor(
    private readonly cfg: ICfg,
    private readonly engine?: IEngine,
    win?: BrowserWindow,
    master = false
  ) {
    super(IHardwares.NAME, master, win)
    this._hardwares = []
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  @method()
  async readHardwares() {
    return this._hardwares
  }

  @method()
  async removeHardware(index: number) {
    this._hardwares.splice(index, 1)

    const changed = await this.writeHardwareCache()
    if (changed) {
      this.emit(IHardwares.OnHardwareList)
    }

    return changed
  }

  @method()
  async addHardware(val: HardwareInfo, index?: number) {
    if (index === undefined || index === null) {
      this._hardwares.push(val)
    } else {
      this._hardwares.splice(index, 0, val)
    }

    const changed = await this.writeHardwareCache()
    if (changed) {
      this.emit(IHardwares.OnHardwareList)
    }

    return changed
  }

  @method()
  async modifyHardware(index: number, val: Partial<HardwareInfo>) {
    if (this._hardwares.length <= index) {
      return false
    }
    const item = this._hardwares[index]
    if (val.name !== undefined) item.name = val.name
    if (val.inter !== undefined) item.inter = val.inter
    if (val.scan_rate !== undefined) item.scan_rate = val.scan_rate
    if (val.inst_addr !== undefined) item.inst_addr = val.inst_addr

    const changed = await this.writeHardwareCache()
    if (changed) {
      this.emit(IHardwares.OnHardwareList)
    }

    return changed
  }

  @method()
  async loadHardware<T>(name: string) {
    return undefined
  }

  @method()
  async saveHardware<T>(name: string) {
    return false
  }

  private get engineName() {
    return this.engine?.engineName || 'common'
  }

  private async loadTimers(force?: boolean) {
    // if (this.engine && !this.engine.engineName) {
    //   return
    // }
    // const { timers: list = [] } = (await this.cfg.read(timers.Timers, this.engineName, force)) || {}
    // this._options.list = list.map(val => {
    //   const { name, param_id: equation, type } = val
    //   return { name, equation, type: kTimerType[type] }
    // })
    // this.emit(ITimer.OnOptions)
  }

  private async writeTimersCache() {
    // if (this.engine && !this.engine.engineName) {
    //   return false
    // }
    // const list: timers.Timer[] = this._options.list.map(val => {
    //   const { name, equation: param_id, type } = val
    //   return new timers.Timer({ name, param_id, type: kTimerTypeVal[type] })
    // })
    // let changed = this.cfg.assign(timers.Timers, { timers: list }, '', this.engineName)
    // changed = changed && (await this.cfg.write(timers.Timers, this.engineName))
    // return changed
  }

  private async writeHardwareCache() {
    const hardware_list: cust.Hardware[] = this._hardwares.map(val => {
      // let printer_type: string | undefined = undefined
      // if (val.type === hardwareTypes.PRINTER) {
      //   const data = val.info as PrinterInfo
      //   printer_type = data?.printer_type
      // }
      return new cust.Hardware(val)
    })
    let changed = this.cfg.assign(cust.Cust, { hardware_list })
    changed = changed && (await this.cfg.write(cust.Cust))

    return changed
  }

  private async loadOptions(force?: boolean) {
    await this.loadTimers(force)
  }

  private async clearOptions() {
    // this.cfg.remove(timers.Timers, '', this.engineName)
  }
}
