import { <PERSON><PERSON><PERSON><PERSON>indow } from 'electron'
import { IVxi, ICfg, IEngine } from '../interfaces'
import { BaseProxy, method } from './Base'

import { vixcards } from '../proto'
import { Vxicard } from 'lib/interfaces/IVxi'

export class Vxi extends BaseProxy<IVxi> implements IVxi {
  private _vxiCards: Vxicard[]

  constructor(
    private readonly cfg: ICfg,
    private readonly engine?: IEngine,
    win?: BrowserWindow,
    master = false
  ) {
    super(IVxi.NAME, master, win)

    this._vxiCards = []
  }

  @method()
  async readDeviceVxicards(): Promise<Array<Vxicard>> {
    return this._vxiCards
  }

  @method()
  async removeDeviceVxicard(str: string): Promise<boolean> {
    const [name, card_type, card_address] = str.split('.')

    this._vxiCards = this._vxiCards.filter(
      card =>
        !(card.name === name && card.card_type === card_type && card.card_address === card_address)
    )

    const changed = await this.writeVxiCardCache(name)
    if (changed) {
      this.emit(IVxi.OnVxiCards)
    }

    return changed
  }
  @method()
  async addDeviceVxicard(val: Vxicard): Promise<boolean> {
    this._vxiCards.push(val)
    const changed = await this.writeVxiCardCache(val.name)
    if (changed) {
      this.emit(IVxi.OnVxiCards)
    }

    return changed
  }
  @method()
  async modifyDeviceVxicard(str: string, val: Partial<Vxicard>): Promise<boolean> {
    const [name, card_type, card_address] = str.split('.')
    const item = this._vxiCards.find(
      card =>
        card.name === name && card.card_type === card_type && card.card_address === card_address
    )
    if (!item) {
      return false
    }
    if (val.name !== undefined) item.name = val.name
    if (val.card_type !== undefined) item.card_type = val.card_type
    if (val.card_address !== undefined) item.card_address = val.card_address
    if (val.no_hardware !== undefined) item.no_hardware = val.no_hardware

    const changed = await this.writeVxiCardCache(name)
    if (changed) {
      this.emit(IVxi.OnVxiCards)
    }

    return changed
  }

  private get engineName() {
    return 'vxi'
  }

  private async writeVxiCardCache(name: string) {
    const vxiCards = this._vxiCards.map(item => new vixcards.VxiCard(item))
    const fileName = `${name}.hw.vxi` // 添加.hw.vxi扩展名
    let changed = this.cfg.assign(vixcards.VxiCards, { cards: vxiCards }, fileName, this.engineName)
    changed = changed && (await this.cfg.write(vixcards.VxiCards, this.engineName, fileName))

    return changed
  }

  override async init() {
    await this.loadOptions()
    await super.init()
  }

  override async clear() {
    await super.clear()
    await this.clearOptions()
  }

  override async load(force = false) {
    await this.loadOptions(force)
    await super.load(force)
  }

  private async loadVxiCards(force?: boolean) {
    const { cards = [] } = (await this.cfg.read(vixcards.VxiCards, this.engineName, force)) || {}
    this._vxiCards = cards
    this.emit(IVxi.OnVxiCards)
  }

  private async loadOptions(force?: boolean) {
    await this.loadVxiCards(force)
  }

  private async clearOptions() {
    this.cfg.remove(vixcards.VxiCards, '', this.engineName)
  }
}
